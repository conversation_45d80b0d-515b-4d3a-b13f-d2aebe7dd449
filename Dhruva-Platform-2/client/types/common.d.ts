// Common type definitions for the Dhruva Platform frontend

// API Response types
interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

interface ApiError {
  response?: {
    data?: {
      detail?: {
        message?: string;
      };
      message?: string;
    };
    status?: number;
  };
  message?: string;
}

// Audio related types
interface AudioConfig {
  sampleRate: number;
  channelCount: number;
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
}

interface RecorderConfig {
  numChannels: number;
  sampleRate?: number;
}

// Component Props types
interface LanguageConfig {
  sourceLanguage: string;
  targetLanguage: string;
}

interface ServiceProps {
  languages: LanguageConfig[];
  serviceId: string;
}

// State management types
interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

interface AudioState extends LoadingState {
  isRecording: boolean;
  audioText: string;
  audioStream: MediaStream | null;
  recorder: any | null;
  timer: number;
  timerInterval: NodeJS.Timeout | null;
}

// Utility types
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Event handler types
type ChangeHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void;
type ClickHandler = (event: React.MouseEvent) => void;
type SubmitHandler = (event: React.FormEvent) => void;

// Toast notification types
interface ToastOptions {
  title: string;
  description?: string;
  status: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  isClosable?: boolean;
}

// Environment variables
interface ProcessEnv {
  NEXT_PUBLIC_API_KEY?: string;
  NODE_ENV: 'development' | 'production' | 'test';
}

declare global {
  namespace NodeJS {
    interface ProcessEnv extends ProcessEnv {}
  }
  
  interface Window {
    Recorder?: any;
    webkitAudioContext?: typeof AudioContext;
  }
}

export {
  ApiResponse,
  ApiError,
  AudioConfig,
  RecorderConfig,
  LanguageConfig,
  ServiceProps,
  LoadingState,
  AudioState,
  Optional,
  RequiredFields,
  ChangeHandler,
  ClickHandler,
  SubmitHandler,
  ToastOptions,
};
