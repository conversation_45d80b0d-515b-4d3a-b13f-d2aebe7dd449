import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Text, Button, VStack, Alert, AlertIcon, AlertTitle, AlertDescription } from '@chakra-ui/react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console and potentially to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // You can also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <Box p={8} maxW="600px" mx="auto" mt={8}>
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <VStack align="start" spacing={4} flex={1}>
              <Box>
                <AlertTitle>Something went wrong!</AlertTitle>
                <AlertDescription>
                  An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
                </AlertDescription>
              </Box>
              
              <VStack spacing={2} align="start" w="full">
                <Button 
                  size="sm" 
                  colorScheme="blue" 
                  onClick={this.handleReset}
                >
                  Try Again
                </Button>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </VStack>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <Box mt={4} p={4} bg="gray.100" borderRadius="md" fontSize="sm" w="full">
                  <Text fontWeight="bold" mb={2}>Error Details (Development Only):</Text>
                  <Text fontFamily="mono" color="red.600" whiteSpace="pre-wrap">
                    {this.state.error.toString()}
                  </Text>
                  {this.state.errorInfo && (
                    <Text fontFamily="mono" color="gray.600" whiteSpace="pre-wrap" mt={2}>
                      {this.state.errorInfo.componentStack}
                    </Text>
                  )}
                </Box>
              )}
            </VStack>
          </Alert>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
