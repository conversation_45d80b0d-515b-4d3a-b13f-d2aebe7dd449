import React from 'react';
import { 
  Box, 
  Spinner, 
  Text, 
  VStack, 
  Center,
  CircularProgress,
  CircularProgressLabel 
} from '@chakra-ui/react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  variant?: 'spinner' | 'circular' | 'dots';
  color?: string;
  thickness?: string;
  progress?: number; // For circular progress with percentage
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message,
  variant = 'spinner',
  color = 'blue.500',
  thickness = '4px',
  progress
}) => {
  const sizeMap = {
    sm: '24px',
    md: '32px',
    lg: '48px',
    xl: '64px'
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'circular':
        return (
          <CircularProgress 
            value={progress} 
            color={color} 
            size={sizeMap[size]}
            thickness={thickness}
            isIndeterminate={progress === undefined}
          >
            {progress !== undefined && (
              <CircularProgressLabel>{progress}%</CircularProgressLabel>
            )}
          </CircularProgress>
        );
      
      case 'dots':
        return (
          <Box className="loading-dots">
            <style jsx>{`
              .loading-dots {
                display: inline-block;
                position: relative;
                width: ${sizeMap[size]};
                height: ${sizeMap[size]};
              }
              .loading-dots div {
                position: absolute;
                top: 50%;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: ${color};
                animation-timing-function: cubic-bezier(0, 1, 1, 0);
              }
              .loading-dots div:nth-child(1) {
                left: 8px;
                animation: loading-dots1 0.6s infinite;
              }
              .loading-dots div:nth-child(2) {
                left: 8px;
                animation: loading-dots2 0.6s infinite;
              }
              .loading-dots div:nth-child(3) {
                left: 32px;
                animation: loading-dots2 0.6s infinite;
              }
              .loading-dots div:nth-child(4) {
                left: 56px;
                animation: loading-dots3 0.6s infinite;
              }
              @keyframes loading-dots1 {
                0% { transform: scale(0); }
                100% { transform: scale(1); }
              }
              @keyframes loading-dots3 {
                0% { transform: scale(1); }
                100% { transform: scale(0); }
              }
              @keyframes loading-dots2 {
                0% { transform: translate(0, 0); }
                100% { transform: translate(24px, 0); }
              }
            `}</style>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </Box>
        );
      
      default:
        return (
          <Spinner 
            size={size} 
            color={color} 
            thickness={thickness}
          />
        );
    }
  };

  return (
    <Center>
      <VStack spacing={4}>
        {renderSpinner()}
        {message && (
          <Text 
            fontSize={size === 'sm' ? 'sm' : 'md'} 
            color="gray.600"
            textAlign="center"
          >
            {message}
          </Text>
        )}
      </VStack>
    </Center>
  );
};

// Overlay loading component for full-screen loading
interface LoadingOverlayProps extends LoadingSpinnerProps {
  isVisible: boolean;
  backdrop?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  backdrop = true,
  ...spinnerProps
}) => {
  if (!isVisible) return null;

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg={backdrop ? "blackAlpha.600" : "transparent"}
      zIndex={9999}
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <Box
        bg="white"
        p={8}
        borderRadius="lg"
        boxShadow="xl"
        minW="200px"
      >
        <LoadingSpinner {...spinnerProps} />
      </Box>
    </Box>
  );
};

export default LoadingSpinner;
