import { Box, Grid, GridItem } from "@chakra-ui/react";
import React, { ReactNode, useState, useCallback } from "react";
import Sidebar from "../Navigation/Sidebar";
import useMediaQuery from "../../hooks/useMediaQuery";

interface DashboardLayoutProps {
  children: ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
}) => {
  const [isBlurred, setIsBlurred] = useState<boolean>(false);
  const isSmallScreen = useMediaQuery("(max-width: 1080px)");

  const handleMouseEnter = useCallback(() => {
    setIsBlurred(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsBlurred(false);
  }, []);

  return (
    <Box>
      <Grid templateColumns="repeat(24, 1fr)">
        <GridItem colSpan={1}>
          <Box
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {!isSmallScreen && <Sidebar />}
          </Box>
        </GridItem>
        <GridItem
          colSpan={23}
          mr={isSmallScreen ? "1%" : "3%"}
          ml={isSmallScreen ? "1%" : "3%"}
          mt="2%"
        >
          <Box opacity={isBlurred ? 0.3 : 1} transition="opacity 0.2s ease">
            {children}
          </Box>
        </GridItem>
      </Grid>
    </Box>
  );
};
