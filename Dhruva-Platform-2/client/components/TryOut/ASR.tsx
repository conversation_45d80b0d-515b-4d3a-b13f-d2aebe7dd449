import {
  Stack,
  Text,
  Select,
  Button,
  Textarea,
  Grid,
  GridItem,
  Progress,
  Input,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Box,
  HStack,
  Spacer,
  useToast,
} from "@chakra-ui/react";
import { FaMicrophone } from "react-icons/fa";
import { useState, useEffect } from "react";
import { dhruvaAPI, apiInstance } from "../../api/apiConfig";
import { lang2label } from "../../config/config";
import { getWordCount } from "../../utils/utils";
import {
  StreamingClient,
  SocketStatus,
} from "@project-sunbird/open-speech-streaming-client";
import { CloseIcon } from "@chakra-ui/icons";
import React from "react";
import { FeedbackModal } from "../Feedback/Feedback";
import {
  PipelineInput,
  PipelineOutput,
  ULCATaskType,
} from "../Feedback/FeedbackTypes";

interface LanguageConfig {
  sourceLanguage: string;
  targetLanguage: string;
}

interface Props {
  languages: LanguageConfig[];
  serviceId: string;
}

const ASRTry: React.FC<Props> = (props) => {
  const [streamingClient, setStreamingClient] = useState(new StreamingClient());

  const [timer, setTimer] = useState(0);
  const [timerInterval, setTimerInterval] = useState(null);

  const [languages, setLanguages] = useState<string[]>([]);
  const [language, setLanguage] = useState("");
  const [audioText, setAudioText] = useState("");
  const [placeholder, setPlaceHolder] = useState(
    "Start Recording for ASR Inference..."
  );
  const [fetching, setFetching] = useState(false);
  const [recording, setRecording] = useState(false);
  const [sampleRate, setSampleRate] = useState<number>(16000);
  const [recorder, setRecorder] = useState<any>(null);
  const [audioStream, setAudioStream] = useState<any>(null);
  const [fetched, setFetched] = useState(false);
  const [responseWordCount, setResponseWordCount] = useState(0);
  const [requestTime, setRequestTime] = useState("");

  const [inferenceMode, setInferenceMode] = useState("rest");

  const [permission, setPermission] = useState<boolean>(true);
  const [modal, setModal] = useState(<></>);
  const [micError, setMicError] = useState<string>("");

  const toast = useToast();

  const [streaming, setStreaming] = useState(false);
  const [streamingText, setStreamingText] = useState("");
  const [pipelineInput, setPipelineInput] = useState<
    PipelineInput | undefined
  >();
  const [pipelineOutput, setPipelineOutput] = useState<
    PipelineOutput | undefined
  >();
  const getASROutput = (asrInput: string) => {
    setFetching(true);
    setMicError("");

    apiInstance
      .post(
        dhruvaAPI.asrInference + `?serviceId=${props.serviceId}`,
        {
          audio: [
            {
              audioContent: asrInput,
            },
          ],
          config: {
            language: {
              sourceLanguage: language,
            },
            serviceId: props.serviceId,
            audioFormat: "wav",
            encoding: "base64",
            samplingRate: sampleRate,
          },
          controlConfig: {
            dataTracking: true,
          },
        },
        {
          headers: {
            accept: "application/json",
            authorization: process.env.NEXT_PUBLIC_API_KEY,
            "Content-Type": "application/json",
          },
        }
      )
      .then((response) => {
        setPipelineInput({
          pipelineTasks: [
            {
              config: {
                language: {
                  sourceLanguage: language,
                },
                audioFormat: "wav",
                encoding: "base64",
                samplingRate: sampleRate,
              },
              taskType: ULCATaskType.ASR,
            },
          ],
          inputData: {
            audio: [
              {
                audioContent: asrInput,
              },
            ],
          },
        });
        setPipelineOutput({
          pipelineResponse: [
            {
              taskType: ULCATaskType.ASR,
              output: response.data.output,
            },
          ],
        });
        var output = response.data.output[0].source;
        setAudioText(output);
        setResponseWordCount(getWordCount(output));
        setRequestTime(response.headers["request-duration"]);
        setFetching(false);
        setFetched(true);
      })
      .catch((error) => {
        console.error("ASR inference error:", error);
        const errorMessage = error.response?.data?.detail?.message ||
                           error.response?.data?.message ||
                           error.message ||
                           "ASR inference failed";
        setMicError(errorMessage);
        setAudioText("[ASR Failed]");
        setFetching(false);
        setFetched(false);

        toast({
          title: "ASR Error",
          description: errorMessage,
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      });
  };

  const handleRecording = (blob: any) => {
    try {
      const reader = new FileReader();
      reader.onloadend = () => {
        try {
          const result = reader.result as string;
          if (!result || !result.includes(',')) {
            throw new Error("Invalid audio data format");
          }

          const base64Data = result.split(",")[1];
          if (!base64Data) {
            throw new Error("Failed to extract audio data");
          }

          // Validate base64 data
          if (base64Data.length < 100) {
            throw new Error("Audio data too short - recording may have failed");
          }

          // Test audio playback
          const audio = new Audio("data:audio/wav;base64," + base64Data);
          audio.onerror = () => {
            console.warn("Audio playback failed, but proceeding with ASR");
          };
          audio.play().catch(() => {
            console.warn("Audio playback failed, but proceeding with ASR");
          });

          getASROutput(base64Data);
        } catch (error) {
          console.error("Error processing recorded audio:", error);
          setMicError(`Recording processing failed: ${error.message}`);
          setFetching(false);
          toast({
            title: "Recording Error",
            description: `Failed to process recording: ${error.message}`,
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      };

      reader.onerror = () => {
        const error = "Failed to read recorded audio data";
        setMicError(error);
        setFetching(false);
        toast({
          title: "Recording Error",
          description: error,
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      };

      reader.readAsDataURL(blob);
    } catch (error) {
      console.error("Error handling recording:", error);
      setMicError(`Recording handling failed: ${error.message}`);
      setFetching(false);
      toast({
        title: "Recording Error",
        description: `Failed to handle recording: ${error.message}`,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const startStreaming = () => {
    try {
      setStreamingText("");
      setStreaming(true);
      setFetching(true);
      setMicError("");

      streamingClient.connect(
        dhruvaAPI.asrStreamingInference,
        props.serviceId,
        process.env.NEXT_PUBLIC_API_KEY,
        language,
        sampleRate,
        [],
        function (action: any, id: any) {
          if (action === SocketStatus.CONNECTED) {
            console.log("Streaming Connected");
            streamingClient.startStreaming(function (transcript: string) {
              setStreamingText(transcript);
            });
          } else if (action === SocketStatus.TERMINATED) {
            console.log("Streaming Terminated");
            setStreaming(false);
            setFetching(false);
          } else if (action === SocketStatus.ERROR) {
            console.error("Streaming Error:", id);
            setMicError(`Streaming error: ${id || 'Connection failed'}`);
            setStreaming(false);
            setFetching(false);
            toast({
              title: "Streaming Error",
              description: `Streaming failed: ${id || 'Connection error'}`,
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          } else {
            console.log("Streaming Action:", action, id);
          }
        }
      );
    } catch (error) {
      console.error("Error starting streaming:", error);
      setMicError(`Failed to start streaming: ${error.message}`);
      setStreaming(false);
      setFetching(false);
      toast({
        title: "Streaming Error",
        description: `Failed to start streaming: ${error.message}`,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const stopStreaming = () => {
    try {
      console.log("Streaming Ended.");
      streamingClient.stopStreaming();
      streamingClient.disconnect();
      setStreaming(false);
      setFetching(false);
    } catch (error) {
      console.error("Error stopping streaming:", error);
      // Still set states to false even if there's an error
      setStreaming(false);
      setFetching(false);
    }
  };

  const startRecording = () => {
    try {
      setMicError("");
      setAudioText("");

      if (!audioStream) {
        throw new Error("Audio stream not available");
      }

      if (!(window as any).Recorder) {
        throw new Error("Recorder library not loaded");
      }

      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContext) {
        throw new Error("AudioContext not supported in this browser");
      }

      const audioContext = new AudioContext();

      // Ensure AudioContext is running
      if (audioContext.state === 'suspended') {
        audioContext.resume();
      }

      const input = audioContext.createMediaStreamSource(audioStream);
      const Recorder = (window as any).Recorder;

      // Configure recorder with proper sample rate
      const recorderConfig = {
        numChannels: 1,
        sampleRate: sampleRate // Use the selected sample rate
      };

      const newRecorder = new Recorder(input, recorderConfig);
      newRecorder.record();
      setRecorder(newRecorder);

      console.log(`Recording Started - Sample Rate: ${sampleRate}Hz, AudioContext Rate: ${audioContext.sampleRate}Hz`);
      setRecording(true);
      setFetched(false);
      setFetching(false); // Don't set fetching true until we stop recording
      setPlaceHolder("Recording Audio....");

      // Start the timer
      setTimer(0);
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);

      // Save the interval ID in the state to clear it later
      setTimerInterval(interval);

    } catch (error) {
      console.error("Error starting recording:", error);
      setMicError(`Failed to start recording: ${error.message}`);
      setRecording(false);
      setFetching(false);
      toast({
        title: "Recording Error",
        description: `Failed to start recording: ${error.message}`,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const stopRecording = () => {
    try {
      console.log("Recording Stopped");
      setRecording(false);

      if (audioStream) {
        audioStream.getAudioTracks()[0].stop();
      }

      if (recorder) {
        // Use the selected sample rate instead of hardcoded 16000
        recorder.exportWAV(handleRecording, "audio/wav", sampleRate);
        recorder.stop();
      }

      setPlaceHolder("Processing Audio...");
      setFetching(true); // Set fetching true when processing starts

      // Clear the timer interval
      if (timerInterval) {
        clearInterval(timerInterval);
        setTimerInterval(null);
      }

    } catch (error) {
      console.error("Error stopping recording:", error);
      setMicError(`Failed to stop recording: ${error.message}`);
      setRecording(false);
      setFetching(false);
      setPlaceHolder("Start Recording for ASR Inference...");

      toast({
        title: "Recording Error",
        description: `Failed to stop recording: ${error.message}`,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    // Only request permissions when not already recording
    if (!recording && !audioStream) {
      navigator.mediaDevices
        .getUserMedia({
          audio: {
            sampleRate: sampleRate,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        })
        .then((stream) => {
          setAudioStream(stream);
          setPermission(true);
          setModal(<></>);
          console.log("Microphone permission granted");
        })
        .catch((e: any) => {
          console.error("Microphone permission error:", e);
          setPermission(false);

          let errorMessage = "Microphone access denied";
          if (e.name === 'NotAllowedError') {
            errorMessage = "Microphone permission denied. Please allow microphone access and refresh the page.";
          } else if (e.name === 'NotFoundError') {
            errorMessage = "No microphone found. Please connect a microphone and refresh the page.";
          } else if (e.name === 'NotReadableError') {
            errorMessage = "Microphone is being used by another application.";
          } else if (e.name === 'OverconstrainedError') {
            errorMessage = "Microphone doesn't support the requested configuration.";
          }

          setMicError(errorMessage);
          setModal(
            <Box
              mt="1rem"
              width={"100%"}
              minH={"3rem"}
              border={"1px"}
              borderColor={"red.300"}
              background={"red.50"}
              borderRadius="md"
            >
              <HStack ml="1rem" mr="1rem" mt="0.6rem">
                <Text color={"red.600"} fontSize="sm">
                  {errorMessage}
                </Text>
                <Spacer />
                <CloseIcon
                  onClick={() => setModal(<></>)}
                  color={"red.600"}
                  fontSize={"xs"}
                  cursor="pointer"
                />
              </HStack>
            </Box>
          );
        });
    }
  }, [sampleRate]); // Re-run when sample rate changes

  useEffect(() => {
    const uniqueSourceLanguages: any = Array.from(
      new Set(
        props.languages.map(
          (language: LanguageConfig) => language.sourceLanguage
        )
      )
    );
    setLanguages(uniqueSourceLanguages);
    setLanguage(uniqueSourceLanguages[0]);
  }, []);

  return (
    <>
      <Grid templateRows="repeat(3)" gap={5}>
        <GridItem>
          <Stack direction={"column"}>
            <Stack direction={"row"}>
              <Text className="dview-service-try-option-title">
                Inference Mode:
              </Text>
              <Select
                onChange={(e) => {
                  setInferenceMode(e.target.value);
                }}
              >
                <option value={"rest"}>REST</option>
                <option value={"streaming"}>Streaming</option>
              </Select>
            </Stack>
            <Stack direction={"row"}>
              <Text className="dview-service-try-option-title">
                Select Language:
              </Text>
              <Select
                onChange={(e) => {
                  setLanguage(e.target.value);
                }}
                value={language}
              >
                {languages.map((language) => (
                  <option key={language} value={language}>
                    {lang2label[language]}
                  </option>
                ))}
              </Select>
            </Stack>
            <Stack direction={"row"}>
              <Text className="dview-service-try-option-title">
                Sample Rate:
              </Text>
              <Select
                value={sampleRate}
                onChange={(e) => {
                  const newSampleRate = Number(e.target.value);
                  setSampleRate(newSampleRate);
                  console.log(`Sample rate changed to: ${newSampleRate}Hz`);
                }}
              >
                <option value={16000}>16000 Hz (Recommended)</option>
                <option value={48000}>48000 Hz</option>
                <option value={8000}>8000 Hz</option>
              </Select>
            </Stack>
          </Stack>
        </GridItem>
        <GridItem>
          {fetching ? <Progress size="xs" isIndeterminate /> : <></>}
        </GridItem>

        {fetched ? (
          <GridItem>
            <SimpleGrid
              p="1rem"
              w="100%"
              h="auto"
              bg="orange.100"
              borderRadius={15}
              columns={2}
              spacingX="40px"
              spacingY="20px"
            >
              <Stat>
                <StatLabel>Word Count</StatLabel>
                <StatNumber>{responseWordCount}</StatNumber>
                <StatHelpText>Response</StatHelpText>
              </Stat>
              <Stat>
                <StatLabel>Response Time</StatLabel>
                <StatNumber>{Number(requestTime) / 1000}</StatNumber>
                <StatHelpText>seconds</StatHelpText>
              </Stat>
            </SimpleGrid>
          </GridItem>
        ) : (
          <></>
        )}
        {inferenceMode === "rest" ? (
          <GridItem>
            <Stack>
              <Textarea
                w={"auto"}
                h={200}
                readOnly
                value={audioText}
                placeholder={placeholder}
              />
              {recording && (
                <Text color={"gray.300"}>
                  Recording Time : {timer} / 120 seconds
                  {timer >= 120 &&
                    toast({
                      title: "Audio time limit exceeded",
                      status: "warning",
                      duration: 3000,
                      isClosable: true,
                    }) &&
                    stopRecording()}
                </Text>
              )}
              {micError && (
                <Box
                  mt="1rem"
                  p="0.5rem"
                  border="1px"
                  borderColor="red.300"
                  background="red.50"
                  borderRadius="md"
                >
                  <Text color="red.600" fontSize="sm">
                    Error: {micError}
                  </Text>
                </Box>
              )}
              <Stack direction={"row"} gap={5}>
                {recording ? (
                  <Button
                    onClick={() => {
                      stopRecording();
                    }}
                  >
                    <FaMicrophone /> Stop
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      if (permission) {
                        startRecording();
                      }
                    }}
                  >
                    <FaMicrophone size={15} />
                  </Button>
                )}
                <Input
                  variant={"unstyled"}
                  onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
                    try {
                      const selectedAudioFile = e.target["files"]?.[0];
                      if (!selectedAudioFile) {
                        return;
                      }

                      // Validate file type
                      const validTypes = ['audio/wav', 'audio/mp3', 'audio/flac', 'audio/ogg'];
                      if (!validTypes.includes(selectedAudioFile.type) &&
                          !selectedAudioFile.name.match(/\.(wav|mp3|flac|ogg)$/i)) {
                        toast({
                          title: "Invalid File Type",
                          description: "Please select a valid audio file (WAV, MP3, FLAC, OGG)",
                          status: "error",
                          duration: 5000,
                          isClosable: true,
                        });
                        return;
                      }

                      // Validate file size (max 10MB)
                      if (selectedAudioFile.size > 10 * 1024 * 1024) {
                        toast({
                          title: "File Too Large",
                          description: "Please select an audio file smaller than 10MB",
                          status: "error",
                          duration: 5000,
                          isClosable: true,
                        });
                        return;
                      }

                      setMicError("");
                      setAudioText("");

                      const selectedAudioReader = new FileReader();

                      selectedAudioReader.onloadend = () => {
                        try {
                          const result = selectedAudioReader.result as string;
                          if (!result || !result.includes(',')) {
                            throw new Error("Invalid file data format");
                          }

                          const base64Data = result.split(",")[1];
                          if (!base64Data) {
                            throw new Error("Failed to extract file data");
                          }

                          // Test audio playback (optional for files)
                          const audio = new Audio(result);
                          audio.onerror = () => {
                            console.warn("Audio playback failed, but proceeding with ASR");
                          };
                          audio.play().catch(() => {
                            console.warn("Audio playback failed, but proceeding with ASR");
                          });

                          getASROutput(base64Data);
                        } catch (error) {
                          console.error("Error processing uploaded file:", error);
                          setMicError(`File processing failed: ${error.message}`);
                          toast({
                            title: "File Processing Error",
                            description: `Failed to process file: ${error.message}`,
                            status: "error",
                            duration: 5000,
                            isClosable: true,
                          });
                        }
                      };

                      selectedAudioReader.onerror = () => {
                        const error = "Failed to read uploaded file";
                        setMicError(error);
                        toast({
                          title: "File Reading Error",
                          description: error,
                          status: "error",
                          duration: 5000,
                          isClosable: true,
                        });
                      };

                      selectedAudioReader.readAsDataURL(selectedAudioFile);
                    } catch (error) {
                      console.error("Error handling file upload:", error);
                      setMicError(`File upload failed: ${error.message}`);
                      toast({
                        title: "Upload Error",
                        description: `Failed to upload file: ${error.message}`,
                        status: "error",
                        duration: 5000,
                        isClosable: true,
                      });
                    }
                  }}
                  type={"file"}
                  accept="audio/*"
                />
              </Stack>
            </Stack>
            {pipelineOutput && (
              <FeedbackModal
                pipelineInput={pipelineInput}
                pipelineOutput={pipelineOutput}
                taskType={ULCATaskType.ASR}
              />
            )}
          </GridItem>
        ) : (
          <GridItem>
            <Stack gap={5}>
              <Textarea
                w={"auto"}
                h={200}
                readOnly
                value={streamingText}
                placeholder={placeholder}
              />
              <Stack direction={"column"}>
                {streaming ? (
                  <Button
                    onClick={() => {
                      stopStreaming();
                    }}
                  >
                    <FaMicrophone /> Stop
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      startStreaming();
                    }}
                  >
                    <FaMicrophone size={15} />
                  </Button>
                )}
              </Stack>
            </Stack>
          </GridItem>
        )}
      </Grid>
      {modal}
    </>
  );
};

export default ASRTry;
