{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "react-hooks/exhaustive-deps": "warn", "react/jsx-key": "error", "react/no-unescaped-entities": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error"}, "env": {"browser": true, "es2021": true, "node": true}}